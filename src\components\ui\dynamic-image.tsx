import React, { useState, useEffect } from 'react';
import { getImageConfig, fetchImageUrl, imageUrls } from '@/utils/imageUrls';

interface DynamicImageProps {
  imageKey: keyof typeof imageUrls;
  className?: string;
  loading?: 'lazy' | 'eager';
  onError?: () => void;
  onLoad?: () => void;
}

/**
 * Dynamic Image Component
 * Handles image loading with fallback support and error handling
 */
export const DynamicImage: React.FC<DynamicImageProps> = ({
  imageKey,
  className = '',
  loading = 'lazy',
  onError,
  onLoad
}) => {
  const [currentSrc, setCurrentSrc] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const config = getImageConfig(imageKey);

  useEffect(() => {
    const loadImage = async () => {
      setIsLoading(true);
      setHasError(false);
      
      try {
        const imageUrl = await fetchImageUrl(imageKey);
        setCurrentSrc(imageUrl);
      } catch (error) {
        console.error(`Failed to load image for key: ${imageKey}`, error);
        setHasError(true);
        setCurrentSrc(config.fallbackUrl || config.url);
      } finally {
        setIsLoading(false);
      }
    };

    loadImage();
  }, [imageKey, config.fallbackUrl, config.url]);

  const handleImageLoad = () => {
    setIsLoading(false);
    setHasError(false);
    onLoad?.();
  };

  const handleImageError = () => {
    setHasError(true);
    setIsLoading(false);
    
    // Try fallback if not already using it
    if (config.fallbackUrl && currentSrc !== config.fallbackUrl) {
      setCurrentSrc(config.fallbackUrl);
    }
    
    onError?.();
  };

  // Show loading placeholder
  if (isLoading && !currentSrc) {
    return (
      <div className={`bg-gray-200 animate-pulse flex items-center justify-center ${className}`}>
        <div className="text-gray-400 text-xs">Loading...</div>
      </div>
    );
  }

  // Show error placeholder if all attempts failed
  if (hasError && !currentSrc) {
    return (
      <div className={`bg-gray-100 border-2 border-dashed border-gray-300 flex items-center justify-center ${className}`}>
        <div className="text-gray-400 text-xs text-center">
          <div>Image</div>
          <div>Not Found</div>
        </div>
      </div>
    );
  }

  return (
    <img
      src={currentSrc}
      alt={config.alt}
      className={className}
      loading={loading}
      onLoad={handleImageLoad}
      onError={handleImageError}
    />
  );
};

/**
 * Simple Image Component (without dynamic loading)
 * For cases where you just need the configured URL and alt text
 */
export const SimpleImage: React.FC<{
  imageKey: keyof typeof imageUrls;
  className?: string;
  loading?: 'lazy' | 'eager';
}> = ({ imageKey, className = '', loading = 'lazy' }) => {
  const config = getImageConfig(imageKey);

  return (
    <img
      src={config.url}
      alt={config.alt}
      className={className}
      loading={loading}
    />
  );
};
