# Dynamic Image Management System

This system provides centralized management of image URLs with fallback support and dynamic loading capabilities.

## Files Structure

```
src/
├── utils/
│   └── imageUrls.ts          # Image configuration and utilities
└── components/
    └── ui/
        └── dynamic-image.tsx  # Reusable image components
```

## Usage

### 1. Adding New Images

Add new images to the `imageUrls` object in `src/utils/imageUrls.ts`:

```typescript
export const imageUrls = {
  koneLogo: {
    url: "https://www.kone.in/Images/KONE_logo_blue_tcm152-121992.svg?v=2",
    alt: "KONE Logo",
    fallbackUrl: "/images/kone-logo-fallback.png"
  },
  
  // Add your new images here
  companyLogo: {
    url: "https://example.com/company-logo.svg",
    alt: "Company Logo",
    fallbackUrl: "/images/company-fallback.png"
  },
  
  userAvatar: {
    url: "https://api.example.com/user/avatar.jpg",
    alt: "User Avatar"
    // fallbackUrl is optional
  }
};
```

### 2. Using Images in Components

#### Simple Image (Recommended for most cases)
```tsx
import { SimpleImage } from "@/components/ui/dynamic-image";

// In your component
<SimpleImage
  imageKey="koneLogo"
  className="w-16 h-12 object-contain"
/>
```

#### Dynamic Image (With loading states and error handling)
```tsx
import { DynamicImage } from "@/components/ui/dynamic-image";

// In your component
<DynamicImage
  imageKey="koneLogo"
  className="w-16 h-12 object-contain"
  loading="lazy"
  onLoad={() => console.log('Image loaded')}
  onError={() => console.log('Image failed to load')}
/>
```

### 3. Utility Functions

#### Get Image Configuration
```typescript
import { getImageConfig } from "@/utils/imageUrls";

const config = getImageConfig("koneLogo");
// Returns: { url: "...", alt: "KONE Logo", fallbackUrl: "..." }
```

#### Get Image URL Only
```typescript
import { getImageUrl } from "@/utils/imageUrls";

const url = getImageUrl("koneLogo");
// Returns: "https://www.kone.in/Images/KONE_logo_blue_tcm152-121992.svg?v=2"
```

#### Get Alt Text Only
```typescript
import { getImageAlt } from "@/utils/imageUrls";

const alt = getImageAlt("koneLogo");
// Returns: "KONE Logo"
```

#### Dynamic Image Fetching with Error Handling
```typescript
import { fetchImageUrl } from "@/utils/imageUrls";

const imageUrl = await fetchImageUrl("koneLogo");
// Returns the primary URL if accessible, otherwise fallback URL
```

#### Preload Images for Performance
```typescript
import { preloadImages } from "@/utils/imageUrls";

// Preload multiple images
preloadImages(["koneLogo", "companyLogo", "userAvatar"]);
```

## Components

### SimpleImage
- **Purpose**: Basic image rendering with configured URL and alt text
- **Use case**: Most common scenarios where you just need to display an image
- **Features**: Lightweight, no loading states

### DynamicImage
- **Purpose**: Advanced image loading with error handling and loading states
- **Use case**: When you need loading indicators, error handling, or fallback support
- **Features**: Loading states, error handling, automatic fallback, event callbacks

## Benefits

1. **Centralized Management**: All image URLs in one place
2. **Type Safety**: TypeScript ensures you only use defined image keys
3. **Fallback Support**: Automatic fallback to alternative images
4. **Error Handling**: Graceful handling of failed image loads
5. **Performance**: Optional preloading for better user experience
6. **Maintainability**: Easy to update URLs across the entire application
7. **Reusability**: Consistent image handling across components

## Best Practices

1. **Always provide alt text** for accessibility
2. **Use fallback URLs** for critical images
3. **Preload important images** that appear above the fold
4. **Use SimpleImage** for most cases, DynamicImage only when needed
5. **Keep image keys descriptive** and consistent
6. **Store fallback images locally** in the public folder

## Example Migration

### Before (Hardcoded)
```tsx
<img
  src="https://www.kone.in/Images/KONE_logo_blue_tcm152-121992.svg?v=2"
  alt="KONE Logo"
  className="w-16 h-12 object-contain"
/>
```

### After (Dynamic)
```tsx
<SimpleImage
  imageKey="koneLogo"
  className="w-16 h-12 object-contain"
/>
```

This system makes your application more maintainable, type-safe, and provides better error handling for images.
