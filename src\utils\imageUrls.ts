/**
 * Image URLs configuration
 * Centralized management of all image URLs used in the application
 */

export interface ImageConfig {
  url: string;
  alt: string;
  fallbackUrl?: string;
}

export const imageUrls = {
  // Company logos
  koneLogo: {
    url: "https://www.kone.in/Images/KONE_logo_blue_tcm152-121992.svg?v=2",
    alt: "KONE Logo",
    fallbackUrl: "/images/kone-logo-fallback.png"
  },
    yamahalogo: {
    url: "https://www.yamaha-motor-india.com/theme/v4/images/yamaha_logo-black.webp?v=5",
    alt: "YAMAHA Logo",
    fallbackUrl: "/images/kone-logo-fallback.png"
  },
  
  // Add more images as needed
  // companyLogo: {
  //   url: "https://example.com/logo.svg",
  //   alt: "Company Logo",
  //   fallbackUrl: "/images/company-logo-fallback.png"
  // }
} as const;

/**
 * Get image configuration by key
 * @param key - The key of the image in imageUrls object
 * @returns ImageConfig object with url, alt text, and optional fallback
 */
export const getImageConfig = (key: keyof typeof imageUrls): ImageConfig => {
  return imageUrls[key];
};

/**
 * Get image URL by key
 * @param key - The key of the image in imageUrls object
 * @returns The image URL string
 */
export const getImageUrl = (key: keyof typeof imageUrls): string => {
  return imageUrls[key].url;
};

/**
 * Get image alt text by key
 * @param key - The key of the image in imageUrls object
 * @returns The alt text string
 */
export const getImageAlt = (key: keyof typeof imageUrls): string => {
  return imageUrls[key].alt;
};

/**
 * Dynamic image fetcher with error handling
 * @param key - The key of the image in imageUrls object
 * @returns Promise that resolves to the image URL or fallback
 */
export const fetchImageUrl = async (key: keyof typeof imageUrls): Promise<string> => {
  const config = getImageConfig(key);
  
  try {
    // Check if the primary URL is accessible
    const response = await fetch(config.url, { method: 'HEAD' });
    if (response.ok) {
      return config.url;
    }
    throw new Error('Primary image URL not accessible');
  } catch (error) {
    console.warn(`Failed to load primary image for ${key}:`, error);
    // Return fallback URL if available, otherwise return primary URL
    return config.fallbackUrl || config.url;
  }
};

/**
 * Preload images for better performance
 * @param keys - Array of image keys to preload
 */
export const preloadImages = (keys: (keyof typeof imageUrls)[]): void => {
  keys.forEach(key => {
    const config = getImageConfig(key);
    const img = new Image();
    img.src = config.url;
    
    // Also preload fallback if available
    if (config.fallbackUrl) {
      const fallbackImg = new Image();
      fallbackImg.src = config.fallbackUrl;
    }
  });
};
