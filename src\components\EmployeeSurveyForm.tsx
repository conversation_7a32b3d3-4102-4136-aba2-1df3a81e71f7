import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { User, Users, Phone, MapPin, Calendar, Zap, Triangle, Wallet } from "lucide-react";

export const EmployeeSurveyForm = () => {
  const [formData, setFormData] = useState({
    name: "",
    gender: "",
    mobile: "",
    location: "",
    distance: "",
    zone: "",
    route: "",
    cost: ""
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form submitted:", formData);
  };

  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="min-h-screen bg-gray-100 flex">
      {/* Left Sidebar */}
      <div className="w-80 bg-white p-8 flex flex-col items-center justify-center">
        {/* KONE Logo */}
        <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mb-4">
          <span className="text-white text-lg font-bold">K</span>
          <span className="text-xs text-white ml-1">icon</span>
        </div>

        {/* Title */}
        <h1 className="text-2xl font-bold text-gray-800 mb-2">Feedback Form</h1>
        <p className="text-gray-600 text-center text-sm mb-8">Share your valuable insights!</p>

        {/* Feedback Icon */}
        <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center">
          <span className="text-white text-2xl font-bold">!</span>
        </div>
      </div>

      {/* Right Form Area */}
      <div className="flex-1 p-8 flex items-center justify-center">
        <Card className="w-full max-w-4xl shadow-lg">
          <CardContent className="p-8">
            <form onSubmit={handleSubmit}>
              <div className="grid grid-cols-2 gap-6">
                {/* Your Name */}
                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-gray-700 text-sm">
                    <User className="w-4 h-4 text-blue-500" />
                    Your Name:
                  </Label>
                  <Input
                    placeholder="e.g. John Doe"
                    value={formData.name}
                    onChange={(e) => updateFormData("name", e.target.value)}
                    className="border-gray-300 rounded-lg h-12"
                  />
                </div>

                {/* Gender */}
                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-gray-700 text-sm">
                    <Users className="w-4 h-4 text-pink-500" />
                    Gender:
                  </Label>
                  <Select onValueChange={(value) => updateFormData("gender", value)}>
                    <SelectTrigger className="border-gray-300 rounded-lg h-12">
                      <SelectValue placeholder="Select your gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="male">Male</SelectItem>
                      <SelectItem value="female">Female</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Mobile Number */}
                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-gray-700 text-sm">
                    <Phone className="w-4 h-4 text-blue-500" />
                    Mobile Number:
                  </Label>
                  <Input
                    placeholder="Enter your mobile number"
                    value={formData.mobile}
                    onChange={(e) => updateFormData("mobile", e.target.value)}
                    className="border-gray-300 rounded-lg h-12"
                  />
                </div>

                {/* Location */}
                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-gray-700 text-sm">
                    <MapPin className="w-4 h-4 text-red-500" />
                    Location:
                  </Label>
                  <Select onValueChange={(value) => updateFormData("location", value)}>
                    <SelectTrigger className="border-gray-300 rounded-lg h-12">
                      <SelectValue placeholder="Search and select location" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="location1">Location 1</SelectItem>
                      <SelectItem value="location2">Location 2</SelectItem>
                      <SelectItem value="location3">Location 3</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Distance */}
                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-gray-700 text-sm">
                    <Calendar className="w-4 h-4 text-green-500" />
                    Distance (km):
                  </Label>
                  <Input
                    placeholder="e.g. 15"
                    value={formData.distance}
                    onChange={(e) => updateFormData("distance", e.target.value)}
                    className="border-gray-300 rounded-lg h-12"
                  />
                </div>

                {/* Zone */}
                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-gray-700 text-sm">
                    <Zap className="w-4 h-4 text-teal-500" />
                    Zone:
                  </Label>
                  <Input
                    placeholder="e.g. Zone A"
                    value={formData.zone}
                    onChange={(e) => updateFormData("zone", e.target.value)}
                    className="border-gray-300 rounded-lg h-12"
                  />
                </div>

                {/* Route */}
                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-gray-700 text-sm">
                    <Triangle className="w-4 h-4 text-blue-500" />
                    Route:
                  </Label>
                  <Input
                    placeholder="e.g. Route-1"
                    value={formData.route}
                    onChange={(e) => updateFormData("route", e.target.value)}
                    className="border-gray-300 rounded-lg h-12"
                  />
                </div>

                {/* Cost */}
                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-gray-700 text-sm">
                    <Wallet className="w-4 h-4 text-red-500" />
                    Cost:
                  </Label>
                  <Input
                    placeholder="e.g. 100"
                    value={formData.cost}
                    onChange={(e) => updateFormData("cost", e.target.value)}
                    className="border-gray-300 rounded-lg h-12"
                  />
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex justify-end pt-8">
                <Button type="submit" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-base font-medium rounded-full">
                  Submit Feedback ➤
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};