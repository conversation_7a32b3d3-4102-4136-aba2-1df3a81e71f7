import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { User, Users, Phone, MapPin, Calendar, Zap, Triangle, Wallet } from "lucide-react";

export const EmployeeSurveyForm = () => {
  const [formData, setFormData] = useState({
    name: "",
    gender: "",
    mobile: "",
    location: "",
    distance: "",
    zone: "",
    route: "",
    cost: ""
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form submitted:", formData);
  };

  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="min-h-screen bg-gray-100 flex">
      {/* Left Sidebar */}
      <div className="w-80 bg-white p-8 flex flex-col items-center justify-center">
        {/* KONE Icon */}
        <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mb-6">
          <span className="text-white text-2xl font-bold">K</span>
        </div>
        
        {/* Title */}
        <h1 className="text-3xl font-bold text-gray-800 mb-2">Feedback Form</h1>
        <p className="text-gray-600 text-center mb-8">Share your valuable insights!</p>
        
        {/* Feedback Icon */}
        <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center">
          <span className="text-white text-2xl font-bold">!</span>
        </div>
      </div>

      {/* Right Form Area */}
      <div className="flex-1 p-8">
        <form onSubmit={handleSubmit} className="max-w-4xl space-y-6">
          {/* Personal Information Card */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-gray-800">Personal Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-gray-700">
                    <User className="w-4 h-4 text-blue-500" />
                    Your Name:
                  </Label>
                  <Input
                    placeholder="e.g. John Doe"
                    value={formData.name}
                    onChange={(e) => updateFormData("name", e.target.value)}
                    className="border-gray-300 rounded-lg"
                  />
                </div>
                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-gray-700">
                    <Users className="w-4 h-4 text-pink-500" />
                    Gender:
                  </Label>
                  <Select onValueChange={(value) => updateFormData("gender", value)}>
                    <SelectTrigger className="border-gray-300 rounded-lg">
                      <SelectValue placeholder="Select your gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="male">Male</SelectItem>
                      <SelectItem value="female">Female</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact & Location Card */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-gray-800">Contact & Location</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-gray-700">
                    <Phone className="w-4 h-4 text-blue-500" />
                    Mobile Number:
                  </Label>
                  <Input
                    placeholder="Enter your mobile number"
                    value={formData.mobile}
                    onChange={(e) => updateFormData("mobile", e.target.value)}
                    className="border-gray-300 rounded-lg"
                  />
                </div>
                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-gray-700">
                    <MapPin className="w-4 h-4 text-red-500" />
                    Location:
                  </Label>
                  <Select onValueChange={(value) => updateFormData("location", value)}>
                    <SelectTrigger className="border-gray-300 rounded-lg">
                      <SelectValue placeholder="Search and select location" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="location1">Location 1</SelectItem>
                      <SelectItem value="location2">Location 2</SelectItem>
                      <SelectItem value="location3">Location 3</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Route Details Card */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-gray-800">Route Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-gray-700">
                    <Calendar className="w-4 h-4 text-green-500" />
                    Distance (km):
                  </Label>
                  <Input
                    placeholder="e.g. 15"
                    value={formData.distance}
                    onChange={(e) => updateFormData("distance", e.target.value)}
                    className="border-gray-300 rounded-lg"
                  />
                </div>
                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-gray-700">
                    <Zap className="w-4 h-4 text-teal-500" />
                    Zone:
                  </Label>
                  <Input
                    placeholder="e.g. Zone A"
                    value={formData.zone}
                    onChange={(e) => updateFormData("zone", e.target.value)}
                    className="border-gray-300 rounded-lg"
                  />
                </div>
                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-gray-700">
                    <Triangle className="w-4 h-4 text-blue-500" />
                    Route:
                  </Label>
                  <Input
                    placeholder="e.g. Route-1"
                    value={formData.route}
                    onChange={(e) => updateFormData("route", e.target.value)}
                    className="border-gray-300 rounded-lg"
                  />
                </div>
                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-gray-700">
                    <Wallet className="w-4 h-4 text-red-500" />
                    Cost:
                  </Label>
                  <Input
                    placeholder="e.g. 100"
                    value={formData.cost}
                    onChange={(e) => updateFormData("cost", e.target.value)}
                    className="border-gray-300 rounded-lg"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end pt-6">
            <Button type="submit" className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-3 text-lg font-medium rounded-full">
              Submit Feedback ➤
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};