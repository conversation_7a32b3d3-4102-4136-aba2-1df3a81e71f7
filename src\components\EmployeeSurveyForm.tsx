import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { User, Users, Phone, MapPin, Calendar, Zap, Triangle, Wallet } from "lucide-react";
import { SimpleImage } from "@/components/ui/dynamic-image";

export const EmployeeSurveyForm = () => {
  const [formData, setFormData] = useState({
    name: "",
    gender: "",
    mobile: "",
    location: "",
    distance: "",
    zone: "",
    route: "",
    cost: ""
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form submitted:", formData);
  };

  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="min-h-screen bg-gray-100 flex flex-col lg:flex-row">
      {/* Left Sidebar */}
      <div className="w-full lg:w-80 bg-gradient-to-br from-blue-50 to-indigo-100 p-4 sm:p-6 lg:p-8 flex flex-col items-center justify-center relative overflow-hidden min-h-[300px] lg:min-h-screen">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5 hidden lg:block">
          <div className="absolute top-10 left-10 w-20 h-20 bg-blue-600 rounded-full"></div>
          <div className="absolute bottom-20 right-10 w-16 h-16 bg-indigo-600 rounded-full"></div>
          <div className="absolute top-1/2 right-5 w-12 h-12 bg-blue-400 rounded-full"></div>
        </div>

        {/* Content */}
        <div className="relative z-10 flex flex-col items-center w-full">
          {/* KONE Logo */}
          <div className="w-16 h-16 sm:w-20 sm:h-20 bg-white rounded-2xl shadow-lg flex items-center justify-center mb-4 sm:mb-6 border border-blue-100">
            <SimpleImage
              imageKey="yamahalogo"
              className="w-12 h-9 sm:w-16 sm:h-12 object-contain"
            />
          </div>

          {/* Title Section */}
          <div className="text-center mb-4 sm:mb-6 lg:mb-8">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-2 sm:mb-3">Survey Form</h1>
            <div className="w-12 sm:w-16 h-1 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full mx-auto mb-3 sm:mb-4"></div>
            <p className="text-gray-600 text-sm sm:text-base leading-relaxed max-w-xs px-4 sm:px-0">
              Share your valuable insights and help us improve our services
            </p>
          </div>

          {/* Feature Highlights - Hidden on mobile, shown on larger screens */}
          <div className="hidden lg:block space-y-4 w-full max-w-xs">
            <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-xl border border-white/20">
              <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                <User className="w-4 h-4 text-white" />
              </div>
              <span className="text-sm text-gray-700 font-medium">Personal Details</span>
            </div>

            <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-xl border border-white/20">
              <div className="w-8 h-8 bg-indigo-500 rounded-lg flex items-center justify-center">
                <MapPin className="w-4 h-4 text-white" />
              </div>
              <span className="text-sm text-gray-700 font-medium">Location & Route</span>
            </div>

            <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-xl border border-white/20">
              <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                <Zap className="w-4 h-4 text-white" />
              </div>
              <span className="text-sm text-gray-700 font-medium">Quick & Easy</span>
            </div>
          </div>

          {/* Bottom Accent - Smaller on mobile */}
          <div className="mt-4 sm:mt-6 lg:mt-8 w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg">
            <div className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-white rounded-full flex items-center justify-center">
              <span className="text-lg sm:text-xl lg:text-2xl">📝</span>
            </div>
          </div>
        </div>
      </div>

      {/* Right Form Area */}
      <div className="flex-1 p-4 sm:p-6 lg:p-8 flex items-center justify-center">
        <Card className="w-full max-w-4xl shadow-lg">
          <CardContent className="p-4 sm:p-6 lg:p-8">
            <form onSubmit={handleSubmit}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                {/* Your Name */}
                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-gray-700 text-sm">
                    <User className="w-4 h-4 text-blue-500" />
                    Your Name:
                  </Label>
                  <Input
                    placeholder="e.g. John Doe"
                    value={formData.name}
                    onChange={(e) => updateFormData("name", e.target.value)}
                    className="border-gray-300 rounded-lg h-10 sm:h-12"
                  />
                </div>

                {/* Gender */}
                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-gray-700 text-sm">
                    <Users className="w-4 h-4 text-pink-500" />
                    Gender:
                  </Label>
                  <Select onValueChange={(value) => updateFormData("gender", value)}>
                    <SelectTrigger className="border-gray-300 rounded-lg h-10 sm:h-12">
                      <SelectValue placeholder="Select your gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="male">Male</SelectItem>
                      <SelectItem value="female">Female</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Mobile Number */}
                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-gray-700 text-sm">
                    <Phone className="w-4 h-4 text-blue-500" />
                    Mobile Number:
                  </Label>
                  <Input
                    placeholder="Enter your mobile number"
                    value={formData.mobile}
                    onChange={(e) => updateFormData("mobile", e.target.value)}
                    className="border-gray-300 rounded-lg h-10 sm:h-12"
                  />
                </div>

                {/* Location */}
                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-gray-700 text-sm">
                    <MapPin className="w-4 h-4 text-red-500" />
                    Location:
                  </Label>
                  <Select onValueChange={(value) => updateFormData("location", value)}>
                    <SelectTrigger className="border-gray-300 rounded-lg h-10 sm:h-12">
                      <SelectValue placeholder="Search and select location" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="location1">Location 1</SelectItem>
                      <SelectItem value="location2">Location 2</SelectItem>
                      <SelectItem value="location3">Location 3</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Distance */}
                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-gray-700 text-sm">
                    <Calendar className="w-4 h-4 text-green-500" />
                    Distance (km):
                  </Label>
                  <Input
                    placeholder="e.g. 15"
                    value={formData.distance}
                    onChange={(e) => updateFormData("distance", e.target.value)}
                    className="border-gray-300 rounded-lg h-10 sm:h-12"
                  />
                </div>

                {/* Zone */}
                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-gray-700 text-sm">
                    <Zap className="w-4 h-4 text-teal-500" />
                    Zone:
                  </Label>
                  <Input
                    placeholder="e.g. Zone A"
                    value={formData.zone}
                    onChange={(e) => updateFormData("zone", e.target.value)}
                    className="border-gray-300 rounded-lg h-10 sm:h-12"
                  />
                </div>

                {/* Route */}
                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-gray-700 text-sm">
                    <Triangle className="w-4 h-4 text-blue-500" />
                    Route:
                  </Label>
                  <Input
                    placeholder="e.g. Route-1"
                    value={formData.route}
                    onChange={(e) => updateFormData("route", e.target.value)}
                    className="border-gray-300 rounded-lg h-10 sm:h-12"
                  />
                </div>

                {/* Cost */}
                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-gray-700 text-sm">
                    <Wallet className="w-4 h-4 text-red-500" />
                    Cost:
                  </Label>
                  <Input
                    placeholder="e.g. 100"
                    value={formData.cost}
                    onChange={(e) => updateFormData("cost", e.target.value)}
                    className="border-gray-300 rounded-lg h-10 sm:h-12"
                  />
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex justify-center sm:justify-end pt-6 sm:pt-8">
                <Button type="submit" className="bg-blue-600 hover:bg-blue-700 text-white px-6 sm:px-8 py-2 sm:py-3 text-sm sm:text-base font-medium rounded-full w-full sm:w-auto">
                  Submit Feedback ➤
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};